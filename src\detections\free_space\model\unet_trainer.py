import os
import time
from pathlib import Path

import albumentations as A
import cv2 as cv
import matplotlib.pyplot as plt
import numpy as np
import segmentation_models_pytorch as smp
import torch

from src.utils.labelme_to_mask import parse_labelme_to_mask
from src.utils.max_pool import crop_image_to_multiple, max_pool_resize

device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
assert device.type == 'cuda', (
    'GPU not found, please use a GPU to train the model or disable this line.'
)


train_transforms = A.Compose(
    [
        A.HorizontalFlip(p=0.5),
        A.RandomScale(scale_limit=0.15, p=1.0),
        A.<PERSON>otate(limit=15, fill_mask=255, p=1.0),
        A.OneOf(
            [
                A.MultiplicativeNoise(multiplier=(0.8, 1.0), p=1.0),
                <PERSON><PERSON>(p=1.0),
            ],
            p=0.5,
        ),
        <PERSON><PERSON>xe<PERSON>ropout(dropout_prob=0.05, p=0.5),
        <PERSON><PERSON>right<PERSON>ontrast(p=0.2),
        <PERSON><PERSON>mptyMaskIfExists(height=256, width=512, ignore_values=[255], p=1.0),
        A.Normalize(),
    ]
)

test_transforms = A.Compose(
    [
        A.Normalize(),
    ]
)


class Data(torch.utils.data.Dataset):
    def __init__(self, scale, transforms=None):
        self.scale = scale
        self.images, self.masks = self.load_data()
        self.transforms = transforms

    def load_data(self):
        DATA_DIR = 'data/OCR/20250415/images/images_biglabel'

        # Load all json files
        json_files = [f for f in os.listdir(DATA_DIR) if f.endswith('_prep.json')]
        image_files = [f.replace('.json', '.png') for f in json_files]

        images = [cv.imread(str(Path(DATA_DIR) / f), cv.IMREAD_GRAYSCALE) for f in image_files]
        masks = [
            parse_labelme_to_mask(
                str(Path(DATA_DIR) / f),
                unknown_label=255,
                clean_label=0,
                text_label=1,
            )
            for f in json_files
        ]

        masks = [crop_image_to_multiple(mask, self.scale) for mask in masks]
        images = [crop_image_to_multiple(img, self.scale) for img in images]
        masks = [max_pool_resize(mask, 1 / self.scale) for mask in masks]
        images = [cv.resize(img, masks[0].shape[::-1]) for img in images]
        return images, masks

    def __getitem__(self, index):
        image = self.images[index]
        mask = self.masks[index]

        if self.transforms is not None:
            augmented = self.transforms(image=image, mask=mask)
            image = augmented['image']
            mask = augmented['mask']

        # Convert to PyTorch format
        image = image[np.newaxis, :, :]
        mask = mask[np.newaxis, :, :]

        # Convert to tensor
        image = torch.from_numpy(image).float()
        mask = torch.from_numpy(mask).float()

        return image, mask

    def __len__(self):
        return len(self.images)


def train(run):
    Path(f'src/detections/free_space/model/unet/run{run:02d}/checkpoints').mkdir(
        parents=True, exist_ok=False
    )
    Path(f'src/detections/free_space/model/unet/run{run:02d}/vis').mkdir(
        parents=True, exist_ok=False
    )
    with open(f'src/detections/free_space/model/unet/run{run:02d}/loss.txt', 'w') as f:
        f.write('')

    val_image = cv.imread(
        'data/OCR/20250415/images/images_biglabel/20250415_113705057796_prep.png',
        cv.IMREAD_GRAYSCALE,
    )

    dataset = Data(4, train_transforms)
    loader = torch.utils.data.DataLoader(dataset, batch_size=1, shuffle=True)

    model = smp.Unet(
        encoder_name='mobilenet_v2',
        encoder_weights=None,
        in_channels=1,
        classes=1,
    )
    model.to(device)

    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    loss_function = smp.losses.DiceLoss(mode='binary', ignore_index=255)

    # Training loop
    for epoch in range(501):
        model.train()
        train_loss = 0.0
        for _, (image, mask) in enumerate(loader):
            cpu_image = image[0, 0, :, :]
            cpu_mask = mask[0, 0, :, :]
            image = image.to(device)
            mask = mask.to(device)

            # Forward pass
            optimizer.zero_grad()
            output = model(image)

            # Calculate loss
            loss = loss_function(output, mask)
            train_loss += loss.item()

            # Backward pass
            loss.backward()

            # Update weights
            optimizer.step()

        print(f'Epoch {epoch:4d} | Train loss: {train_loss / len(loader):.6f}')
        with open(f'src/detections/free_space/model/unet/run{run:02d}/loss.txt', 'a') as f:
            f.write(f'Epoch {epoch:4d} | Train loss: {train_loss / len(loader):.6f}\n')

        if epoch % 10 == 0:
            torch.save(
                model.state_dict(),
                f'src/detections/free_space/model/unet/run{run:02d}/checkpoints/unet_model_{epoch:04d}.pth',
            )
        if epoch % 5 == 0:
            cpu_output = output.cpu().detach().numpy()[0, 0, :, :]
            fig, ax = plt.subplots(3, 1, sharex=True, sharey=True, figsize=(20, 8))
            ax[0].imshow(cpu_image, cmap='viridis')
            ax[0].set_title('Image')
            ax[1].imshow(cpu_mask, cmap='viridis')
            ax[1].set_title('Mask')
            ax[2].imshow(cpu_output, cmap='viridis')
            ax[2].set_title('Prediction')
            # save to file
            fig.savefig(
                f'src/detections/free_space/model/unet/run{run:02d}/vis/unet_model_{epoch:04d}.png'
            )
            plt.close(fig)
            test(
                model,
                val_image,
                vis_to_file=f'src/detections/free_space/model/unet/run{run:02d}/vis/unet_model_val_{epoch:04d}.png',
            )


def infer(img, visualize=False, vis_to_file=None):
    model = smp.Unet(
        encoder_name='mobilenet_v2',
        encoder_weights=None,
        in_channels=1,
        classes=1,
    )
    model.load_state_dict(
        torch.load('src/detections/free_space/model/unet/run10/checkpoints/unet_model_0220.pth')
    )
    model.to(device)
    result = test(model, img, visualize, vis_to_file)
    # upscale result to original image size
    result = cv.resize(result, (img.shape[1], img.shape[0]))
    return result


def test(model, img, visualize=False, vis_to_file=None):
    model.eval()

    # Apply the same preprocessing as during training
    scale = 4

    img = crop_image_to_multiple(img, scale)

    # Resize to 1/4 size to match training data
    target_height = img.shape[0] // scale
    target_width = img.shape[1] // scale
    img_processed = cv.resize(img, (target_width, target_height))

    img_processed = test_transforms(image=img_processed)['image']

    img_tensor = img_processed[np.newaxis, np.newaxis, :, :]
    image = torch.from_numpy(img_tensor).float().to(device)

    start = time.perf_counter()
    # Forward pass
    with torch.no_grad():  # Disable gradient computation for inference
        output = model(image)
    end = time.perf_counter()
    print(f'Inference time: {end - start:.6f} seconds')

    # Convert to numpy
    cpu_output = output.cpu().detach().numpy()[0, 0, :, :]

    # Resize output back to processed image size for visualization
    if visualize or vis_to_file:
        # Resize output to match the processed input size for fair comparison
        output_resized = cv.resize(cpu_output, (img_processed.shape[1], img_processed.shape[0]))

        fig, ax = plt.subplots(3, 1, sharex=True, sharey=True, figsize=(20, 8))
        ax[0].imshow(img_processed, cmap='viridis')
        ax[0].set_title('Processed Input Image')
        ax[1].imshow(output_resized, cmap='viridis')
        ax[1].set_title('Prediction')
        ax[2].imshow(output_resized > 0.5, cmap='viridis')
        ax[2].set_title('Prediction binary')
        fig.tight_layout()
        if vis_to_file:
            fig.savefig(vis_to_file)
        if visualize:
            plt.show()
        plt.close(fig)

    return cpu_output


if __name__ == '__main__':
    # train(10)
    infer(
        cv.imread(
            'data/OCR/20250415/images/images_biglabel/20250415_113705057796_prep.png',
            cv.IMREAD_GRAYSCALE,
        ),
        visualize=True,
    )
