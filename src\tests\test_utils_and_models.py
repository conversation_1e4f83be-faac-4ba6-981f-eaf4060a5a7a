import json
import os
import tempfile

import numpy as np
import pytest

from src.detections.free_space.lir_basis import largest_interior_rectangle
from src.detections.free_space.model.unet_infer import infer as unet_infer
from src.utils.labelme_to_mask import parse_labelme_to_mask
from src.utils.max_pool import max_pool_resize


class TestUnetInfer:
    """Test cases for unet_infer function."""

    def test_unet_infer_basic(self):
        """Test basic unet inference with small image."""
        # Create a small test image (32x32 grayscale)
        test_image = np.random.randint(0, 255, (32, 32), dtype=np.uint8)

        # Run inference
        result = unet_infer(test_image)

        # Check output properties
        assert isinstance(result, np.ndarray)
        assert result.shape == test_image.shape
        assert result.dtype == np.float64 or result.dtype == np.float32
        # Output is raw logits, so can be any real number
        assert np.isfinite(result).all()  # Check for no NaN or inf values

    def test_unet_infer_different_sizes(self):
        """Test unet inference with different image sizes."""
        sizes = [(16, 16), (32, 32), (64, 64)]

        for height, width in sizes:
            test_image = np.random.randint(0, 255, (height, width), dtype=np.uint8)
            result = unet_infer(test_image)

            assert result.shape == (height, width)
            assert np.isfinite(result).all()  # Check for no NaN or inf values

    def test_unet_infer_edge_cases(self):
        """Test unet inference with edge case inputs."""
        # All black image
        black_image = np.zeros((32, 32), dtype=np.uint8)
        result_black = unet_infer(black_image)
        assert result_black.shape == (32, 32)

        # All white image
        white_image = np.full((32, 32), 255, dtype=np.uint8)
        result_white = unet_infer(white_image)
        assert result_white.shape == (32, 32)

        # Check that results are different for different inputs
        assert not np.array_equal(result_black, result_white)

    def test_unet_infer_deterministic(self):
        """Test that unet inference is deterministic for same input."""
        test_image = np.random.randint(0, 255, (32, 32), dtype=np.uint8)

        # Run inference twice on same image
        result1 = unet_infer(test_image)
        result2 = unet_infer(test_image)

        # Results should be identical
        assert np.array_equal(result1, result2)

    def test_unet_infer_output_type(self):
        """Test that unet inference returns correct data type."""
        test_image = np.random.randint(0, 255, (32, 32), dtype=np.uint8)
        result = unet_infer(test_image)

        # Should return float32 numpy array
        assert isinstance(result, np.ndarray)
        assert result.dtype == np.float32


class TestLirBasis:
    """Test cases for largest_interior_rectangle function."""

    def test_lir_basic_rectangle(self):
        """Test LIR with a simple rectangular grid."""
        # Create a 5x5 grid with a 3x3 True rectangle in the center
        grid = np.array(
            [
                [False, False, False, False, False],
                [False, True, True, True, False],
                [False, True, True, True, False],
                [False, True, True, True, False],
                [False, False, False, False, False],
            ]
        )

        result = largest_interior_rectangle(grid)

        # Should return [x, y, width, height]
        assert len(result) == 4  # x, y, width, height
        assert result[2] == 3 and result[3] == 3  # width=3, height=3

    def test_lir_empty_grid(self):
        """Test LIR with empty grid."""
        grid = np.array([[False, False, False], [False, False, False], [False, False, False]])

        result = largest_interior_rectangle(grid)
        # Should return [0, 0, 0, 0] for empty grid
        assert result[2] == 0 and result[3] == 0  # width=0, height=0

    def test_lir_full_grid(self):
        """Test LIR with completely filled grid."""
        grid = np.array([[True, True, True], [True, True, True], [True, True, True]])

        result = largest_interior_rectangle(grid)
        # Should return [x, y, 3, 3] for full 3x3 grid
        assert result[2] == 3 and result[3] == 3  # width=3, height=3

    def test_lir_with_constraints(self):
        """Test LIR with minimum width/height constraints."""
        grid = np.array([[True, True, False], [True, True, False], [False, False, False]])

        # Without constraints, should find 2x2
        result_no_constraint = largest_interior_rectangle(grid)
        assert result_no_constraint[2] == 2 and result_no_constraint[3] == 2  # width=2, height=2

        # With min_width=3, should find nothing
        result_with_constraint = largest_interior_rectangle(grid, min_width=3)
        assert (
            result_with_constraint[2] == 0 and result_with_constraint[3] == 0
        )  # width=0, height=0

    def test_lir_l_shape(self):
        """Test LIR with L-shaped region."""
        grid = np.array(
            [
                [True, True, False, False],
                [True, True, False, False],
                [True, True, True, True],
                [True, True, True, True],
            ]
        )

        result = largest_interior_rectangle(grid)
        # Should find either 2x4 or 4x2 rectangle
        area = result[2] * result[3]  # width * height
        assert area == 8  # Maximum possible area

    def test_lir_single_pixel(self):
        """Test LIR with single True pixel."""
        grid = np.array([[False, False, False], [False, True, False], [False, False, False]])

        result = largest_interior_rectangle(grid)
        # Should find 1x1 rectangle
        assert result[2] == 1 and result[3] == 1  # width=1, height=1
        assert result[0] == 1 and result[1] == 1  # x=1, y=1

    def test_lir_min_area_constraint(self):
        """Test LIR with minimum area constraint."""
        grid = np.array([[True, True, False], [True, True, False], [False, False, True]])

        # Without area constraint, should find 2x2 rectangle (area=4)
        result_no_constraint = largest_interior_rectangle(grid)
        assert result_no_constraint[2] * result_no_constraint[3] == 4

        # With min_area=5, should find nothing
        result_with_constraint = largest_interior_rectangle(grid, min_area=5)
        assert result_with_constraint[2] == 0 and result_with_constraint[3] == 0


class TestLabelmeMask:
    """Test cases for labelme_to_mask function."""

    def create_test_labelme_json(self, shapes, width=100, height=100):
        """Helper to create test LabelMe JSON data."""
        return {'imageWidth': width, 'imageHeight': height, 'shapes': shapes}

    def test_labelme_basic_polygon(self):
        """Test basic polygon parsing."""
        # Create a simple square polygon labeled as 'clean'
        shapes = [
            {
                'label': 'clean',
                'points': [[10, 10], [40, 10], [40, 40], [10, 40]],
                'shape_type': 'polygon',
            }
        ]

        json_data = self.create_test_labelme_json(shapes)

        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(json_data, f)
            temp_path = f.name

        try:
            mask = parse_labelme_to_mask(temp_path)

            # Check mask properties
            assert mask.shape == (100, 100)
            assert mask.dtype == np.uint8

            # Check that clean area is labeled as 1
            assert mask[20, 20] == 1  # Inside the polygon
            assert mask[5, 5] == 0  # Outside the polygon

        finally:
            os.unlink(temp_path)

    def test_labelme_overlapping_polygons(self):
        """Test overlapping clean and text polygons."""
        shapes = [
            {
                'label': 'clean',
                'points': [[10, 10], [50, 10], [50, 50], [10, 50]],
                'shape_type': 'polygon',
            },
            {
                'label': 'text',
                'points': [[30, 30], [70, 30], [70, 70], [30, 70]],
                'shape_type': 'polygon',
            },
        ]

        json_data = self.create_test_labelme_json(shapes)

        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(json_data, f)
            temp_path = f.name

        try:
            mask = parse_labelme_to_mask(temp_path)

            # Check different regions
            assert mask[20, 20] == 1  # Clean only area
            assert mask[60, 60] == 2  # Text only area
            assert mask[40, 40] == 2  # Overlapping area (text should override)
            assert mask[5, 5] == 0  # Background

        finally:
            os.unlink(temp_path)

    def test_labelme_custom_labels(self):
        """Test custom label mapping."""
        shapes = [
            {
                'label': 'clean',
                'points': [[10, 10], [30, 10], [30, 30], [10, 30]],
                'shape_type': 'polygon',
            }
        ]

        json_data = self.create_test_labelme_json(shapes)

        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(json_data, f)
            temp_path = f.name

        try:
            mask = parse_labelme_to_mask(
                temp_path, unknown_label=100, clean_label=200, text_label=255
            )

            assert mask[20, 20] == 200  # Clean area with custom label
            assert mask[5, 5] == 100  # Background with custom label

        finally:
            os.unlink(temp_path)

    def test_labelme_missing_dimensions(self):
        """Test error handling for missing image dimensions."""
        shapes = []
        json_data = {'shapes': shapes}  # Missing imageWidth and imageHeight

        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(json_data, f)
            temp_path = f.name

        try:
            with pytest.raises(ValueError, match='imageHeight and imageWidth must be present'):
                parse_labelme_to_mask(temp_path)
        finally:
            os.unlink(temp_path)

    def test_labelme_empty_shapes(self):
        """Test parsing with no shapes."""
        shapes = []
        json_data = self.create_test_labelme_json(shapes, 50, 50)

        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(json_data, f)
            temp_path = f.name

        try:
            mask = parse_labelme_to_mask(temp_path)

            # Should be all zeros (unknown/background)
            assert mask.shape == (50, 50)
            assert np.all(mask == 0)

        finally:
            os.unlink(temp_path)

    def test_labelme_unknown_label(self):
        """Test parsing with unknown label (should be ignored)."""
        shapes = [
            {
                'label': 'unknown_label',
                'points': [[10, 10], [30, 10], [30, 30], [10, 30]],
                'shape_type': 'polygon',
            },
            {
                'label': 'clean',
                'points': [[40, 40], [60, 40], [60, 60], [40, 60]],
                'shape_type': 'polygon',
            },
        ]

        json_data = self.create_test_labelme_json(shapes)

        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(json_data, f)
            temp_path = f.name

        try:
            mask = parse_labelme_to_mask(temp_path)

            # Unknown label should be ignored, only clean should be labeled
            assert mask[20, 20] == 0  # Unknown label area should remain 0
            assert mask[50, 50] == 1  # Clean area should be 1

        finally:
            os.unlink(temp_path)


class TestMaxPool:
    """Test cases for max_pool_resize function."""

    def test_max_pool_basic(self):
        """Test basic max pooling operation."""
        # Create a 4x4 mask with known pattern
        mask = np.array([[0, 1, 0, 0], [0, 0, 0, 1], [1, 0, 0, 0], [0, 0, 1, 0]])

        # Pool with scale factor 0.5 (2x2 -> 1x1 pooling)
        result = max_pool_resize(mask, 0.5)

        # Should be 2x2 output
        assert result.shape == (2, 2)

        # Check max pooling results
        assert result[0, 0] == 1  # max(0,1,0,0) = 1
        assert result[0, 1] == 1  # max(0,0,0,1) = 1
        assert result[1, 0] == 1  # max(1,0,0,0) = 1
        assert result[1, 1] == 1  # max(0,0,1,0) = 1

    def test_max_pool_quarter_scale(self):
        """Test max pooling with quarter scale."""
        # Create an 8x8 mask
        mask = np.zeros((8, 8))
        mask[1, 1] = 1  # Single pixel in first quadrant
        mask[3, 6] = 1  # Single pixel in second quadrant
        mask[6, 2] = 1  # Single pixel in third quadrant
        mask[7, 7] = 1  # Single pixel in fourth quadrant

        # Pool with scale factor 0.25 (4x4 -> 1x1 pooling)
        result = max_pool_resize(mask, 0.25)

        # Should be 2x2 output
        assert result.shape == (2, 2)
        assert result[0, 0] == 1  # Contains the pixel at (1,1)
        assert result[0, 1] == 1  # Contains the pixel at (3,6)
        assert result[1, 0] == 1  # Contains the pixel at (6,2)
        assert result[1, 1] == 1  # Contains the pixel at (7,7)

    def test_max_pool_no_scaling(self):
        """Test max pooling with scale factor 1.0 (no change)."""
        mask = np.array([[1, 0, 1], [0, 1, 0], [1, 0, 1]])

        result = max_pool_resize(mask, 1.0)

        # Should be identical to input
        assert np.array_equal(result, mask)

    def test_max_pool_invalid_scale(self):
        """Test error handling for invalid scale factors."""
        mask = np.array([[1, 0], [0, 1]])

        # Scale factor > 1 should raise assertion error
        with pytest.raises(AssertionError, match='Scale factor must be at most 1'):
            max_pool_resize(mask, 1.5)

    def test_max_pool_non_integer_reciprocal(self):
        """Test error handling for non-integer reciprocal scale factors."""
        mask = np.array([[1, 0, 1], [0, 1, 0], [1, 0, 1]])

        # Scale factor that doesn't have integer reciprocal
        with pytest.raises(
            AssertionError, match='Scale factor must be reciprocal of an integer'
        ):
            max_pool_resize(mask, 0.3)

    def test_max_pool_dimension_mismatch(self):
        """Test error handling for dimension mismatches."""
        # Create 5x5 mask (not divisible by 2)
        mask = np.ones((5, 5))

        # Scale factor 0.5 requires dimensions divisible by 2
        with pytest.raises(
            AssertionError, match='Mask height must be a multiple of kernel size'
        ):
            max_pool_resize(mask, 0.5)

    def test_max_pool_preserves_max_values(self):
        """Test that max pooling preserves maximum values in each region."""
        # Create mask with different values in each 2x2 region
        mask = np.array(
            [
                [0.1, 0.9, 0.2, 0.8],
                [0.3, 0.7, 0.4, 0.6],
                [0.5, 0.5, 0.1, 0.9],
                [0.2, 0.8, 0.3, 0.7],
            ]
        )

        result = max_pool_resize(mask, 0.5)

        # Check that maximum values are preserved
        assert result[0, 0] == 0.9  # max of top-left 2x2
        assert result[0, 1] == 0.8  # max of top-right 2x2
        assert result[1, 0] == 0.8  # max of bottom-left 2x2
        assert result[1, 1] == 0.9  # max of bottom-right 2x2


if __name__ == '__main__':
    pytest.main([__file__])
