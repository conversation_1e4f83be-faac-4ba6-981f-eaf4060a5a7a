import asyncio
from typing import Any, Optional, Protocol, Type

from heliovision.communication.opcua import OPCUAClient
from loguru import logger

from src.communication.opcua_nodes import OPCUANodes


class OPCClientProtocol(Protocol):
    async def initialize(
        self,
        heartbeat_send_node: OPCUANodes,
        heartbeat_monitor_node: OPCUANodes,
        heartbeat_send_period: float = 2.0,
        heartbeat_monitor_timeout: float = 2.0,
    ) -> None: ...

    async def send(self, node: OPCUANodes, value: Any) -> None: ...

    async def receive(self, node: OPCUANodes) -> Any: ...

    async def await_for_value(self, node: OPCUANodes, value: Any) -> Any: ...

    async def await_for_nonzero_value(self, node: OPCUAN<PERSON>, period: float = 0.5) -> Any: ...


class OPCClient:
    def __init__(
        self,
        server_url: str,
        node_descriptions: Type[OPCUANodes],
        namespace: Optional[str] = None,
    ) -> None:
        self._client = OPCUAClient(server_url, namespace_name=namespace)
        self._node_descriptions = node_descriptions
        self._nodes = {}
        self._heartbeat_task: Optional[asyncio.Task] = None
        self._monitor_heartbeat_task: Optional[asyncio.Task] = None

    async def initialize(
        self,
        heartbeat_send_node: OPCUANodes,
        heartbeat_monitor_node: OPCUANodes,
        heartbeat_send_period: float = 2.0,
        heartbeat_monitor_timeout: float = 2.0,
    ) -> None:
        """Initialize the OPC UA client."""
        await self._client.connect()
        self._nodes = {
            name: await self._client.get_node(node.node_id, node.data_type)
            for name, node in self._node_descriptions.get_all_nodes().items()
        }
        self._start_heartbeat_task(heartbeat_send_node, heartbeat_send_period)
        # self._start_monitor_heartbeat_task(heartbeat_monitor_node, heartbeat_monitor_timeout)
        logger.debug('OPC UA client initialized and connected to server')

    def _start_heartbeat_task(self, node: OPCUANodes, period: float = 2.0) -> None:
        """Start a heartbeat task for the given node."""
        if self._heartbeat_task:
            logger.warning('Heartbeat task already running.')
            return
        if node.name not in self._nodes:
            raise KeyError(f'Node {node.name} not found in the client')
        self._heartbeat_task = asyncio.create_task(self._heartbeat(node, period))
        logger.debug(f'Started heartbeat task for node {node.name} with period {period} seconds')

    async def _heartbeat(self, node: OPCUANodes, period: float = 2.0) -> None:
        """Periodically send a heartbeat signal to the node."""
        node_name = node.name
        value = False
        if node_name not in self._nodes:
            raise KeyError(f'Node {node_name} not found in the client')
        while True:
            try:
                value = not value  # Toggle the heartbeat value
                logger.trace(f'Setting heartbeat to {value}')
                await self._nodes[node_name].set(value)
                await asyncio.sleep(period)
            except Exception as e:
                logger.exception(f'Error while sending heartbeat to node {node_name}: {e}')
                break

    def _start_monitor_heartbeat_task(self, node: OPCUANodes, timeout: float = 2.0) -> None:
        """Monitor the heartbeat of the given node."""
        if self._monitor_heartbeat_task:
            logger.warning('Heartbeat monitor task already running.')
            return
        if node.name not in self._nodes:
            raise KeyError(f'Node {node.name} not found in the client')
        self._monitor_heartbeat_task = asyncio.create_task(
            self._monitor_heartbeat(node, timeout)
        )
        logger.debug(
            f'Started heartbeat monitor task for node {node.name} with timeout {timeout} seconds'
        )

    async def _monitor_heartbeat(self, node: OPCUANodes, timeout: float = 2.0) -> None:
        """Monitor the heartbeat of the node and log its status."""
        node_name = node.name
        if node_name not in self._nodes:
            raise KeyError(f'Node {node_name} not found in the client')
        value = await self._nodes[node_name].get()
        while True:
            try:
                await asyncio.wait_for(self.await_for_value(node, not value), timeout=timeout)
                value = not value  # Toggle the heartbeat value
                logger.trace(f'Heartbeat for node {node_name} is alive: {value}')
            except asyncio.TimeoutError:
                raise TimeoutError(
                    f'Heartbeat for node {node_name} did not respond within {timeout} seconds'
                )
            except Exception as e:
                logger.exception(f'Error while monitoring heartbeat for node {node_name}: {e}')
                break

    async def send(self, node: OPCUANodes, value: Any) -> None:
        """Send a value to a node."""
        node_name = node.name
        if node_name not in self._nodes:
            raise KeyError(f'Node {node_name} not found in the client')
        required_type = self._node_descriptions.get_node(node_name).data_type
        if isinstance(value, list):
            if not all(isinstance(v, required_type) for v in value):
                raise ValueError(f'Value type mismatch for node {node_name}')
        elif not isinstance(value, required_type):
            raise ValueError(f'Value type mismatch for node {node_name}')
        try:
            await self._nodes[node_name].set(value)
        except Exception as e:
            logger.exception(f'Error while setting value for node {node_name}: {e}')

    async def receive(self, node: OPCUANodes) -> Any:
        """Receive a value from a node."""
        try:
            node_name = node.name
            if node_name not in self._nodes:
                raise KeyError(f'Node {node_name} not found in the client')
        except KeyError:
            raise ValueError(f'Node {node_name} not found in the client')
        return await self._nodes[node_name].get()

    async def await_for_value(self, node: OPCUANodes, value: Any) -> Any:
        try:
            node_name = node.name
            if node_name not in self._nodes:
                raise KeyError(f'Node {node_name} not found in the client')
        except KeyError:
            raise ValueError(f'Node {node_name} not found in the client')
        return await self._nodes[node_name].wait_for_value(value)

    async def await_for_nonzero_value(self, node: OPCUANodes, period: float = 0.5) -> Any:
        try:
            node_name = node.name
            if node_name not in self._nodes:
                raise KeyError(f'Node {node_name} not found in the client')
        except KeyError:
            raise ValueError(f'Node {node_name} not found in the client')
        while True:
            try:
                res = await self._nodes[node_name].get()
                if res != 0:
                    return res
                else:
                    await asyncio.sleep(period)
            except Exception:
                logger.exception('Error while waiting for non-zero value')
                raise
