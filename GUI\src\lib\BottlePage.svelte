<script lang="ts">
    import { Card } from "@heliovision/sveltestrap";
    import ImagePart from "./ImagePart.svelte";
    import { gui } from "../stores.js";

    const state = gui.state;

    $: passport = $state.cylinder?.passport ?? {};
    $: console.log($state)


    $: visionLeft = [{
        title: 'Eigenaar',
        value: passport.owner_name,
    }, {
        title: 'Nr. eigenaar',
        value: passport.serial_number,
    }, {
        title: 'Fabrikant',
        value: passport.manufacturer_name,
    }, {
        title: 'Serienummer fabrikant',
        value: passport.manufacturer_serial_number,
    }, {
        title: 'Fabrikage datum',
        value: passport.manufacturing_date,
    }]

    $: visionRight = [{
        title: 'Laatste keurdatum',
        value: passport.last_test_date,
    }, {
        title: 'Testdruk',
        value: passport.test_pressure,
    }, {
        title: 'Capaciteit',
        value: passport.capacity,
    }, {
        title: 'Oorspronkelijke tarra',
        value: passport.original_tare_weight,
    }, {
        title: '<PERSON>dd<PERSON><PERSON>',
        value: passport.wall_thickness,
    }]

</script>

<style>
.half {
    width: 100%;
    height: 30vh;
    position: relative;
    display: flex;
}

.info-div {
    width: 100%;
    height: 100%;
    margin-top: 0px;
}

.header {
    width: 100%;
    height: 10%;
    font-size: 30px;
    font-weight: bold;
    text-decoration: underline;
    display: flex;
    justify-content: center;
    margin-bottom: 5px;
    
}

.list-items {
    width: 50%;
    height: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding-top: 10px;
}

.item {
    width: 90%;
    height: 20%;
    gap: 5px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 2px solid gray;
}

.item-text {
    width: 60%;
    height: 100%;
    background-color: transparent;
    text-overflow: ellipsis;
    display: flex;
    align-items: center;
    font-size: 25px;
}

.item-value {
    width: 50%;
    height: 75%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid black;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    padding: 2px;
    font-size: 25px;
}

</style>



<Card title="" color="primary-subtle" style="margin-top: -40px; display: flex; justify-content: center; position: relative;">
    <div class="half">
        
        <div style="width: 100%; dipslay: flex; justify; content: center; align-items: center;">
            <ImagePart />
        </div>
        
    </div>
    <div style="margin-top: 5px;">
        <h4 style="text-align: center;">Barcode: {passport.barcode}</h4>
    </div>
    <div class="half" style="height: 45vh;">
        <div class="info-div">
            <div class="header">
                Vision
            </div>
            <div style="height: 90%; width: 100%; display: flex;">
                <div class="list-items" style="border-right: 1px solid black;">
                    {#each visionLeft as item, index}
                        <div class="item"><div class="item-text">{index + 1}. {item.title}:</div><div class="item-value">{item.value}</div></div>
                    {/each}
                </div>
                <div class="list-items" style="border-left: 1px solid black;">
                    {#each visionRight as item, index}
                        <div class="item"><div class="item-text">{index + 6}. {item.title}:</div><div class="item-value">{item.value}</div></div>
                    {/each}
                </div>
            </div>
        </div>
    </div>
</Card>
