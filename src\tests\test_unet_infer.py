import numpy as np
import pytest

from src.detections.free_space.model.unet_infer import infer as unet_infer


class TestUnetInfer:
    """Test cases for unet_infer function."""

    def test_unet_infer_basic(self):
        """Test basic unet inference with small image."""
        # Create a small test image (32x32 grayscale)
        test_image = np.random.randint(0, 255, (32, 32), dtype=np.uint8)
        
        # Run inference
        result = unet_infer(test_image)
        
        # Check output properties
        assert isinstance(result, np.ndarray)
        assert result.shape == test_image.shape
        assert result.dtype == np.float64 or result.dtype == np.float32
        # Output is raw logits, so can be any real number
        assert np.isfinite(result).all()  # Check for no NaN or inf values

    def test_unet_infer_different_sizes(self):
        """Test unet inference with different image sizes."""
        sizes = [(16, 16), (32, 32), (64, 64)]
        
        for height, width in sizes:
            test_image = np.random.randint(0, 255, (height, width), dtype=np.uint8)
            result = unet_infer(test_image)
            
            assert result.shape == (height, width)
            assert np.isfinite(result).all()  # Check for no NaN or inf values

    def test_unet_infer_edge_cases(self):
        """Test unet inference with edge case inputs."""
        # All black image
        black_image = np.zeros((32, 32), dtype=np.uint8)
        result_black = unet_infer(black_image)
        assert result_black.shape == (32, 32)
        
        # All white image
        white_image = np.full((32, 32), 255, dtype=np.uint8)
        result_white = unet_infer(white_image)
        assert result_white.shape == (32, 32)
        
        # Check that results are different for different inputs
        assert not np.array_equal(result_black, result_white)

    def test_unet_infer_deterministic(self):
        """Test that unet inference is deterministic for same input."""
        test_image = np.random.randint(0, 255, (32, 32), dtype=np.uint8)
        
        # Run inference twice on same image
        result1 = unet_infer(test_image)
        result2 = unet_infer(test_image)
        
        # Results should be identical
        assert np.array_equal(result1, result2)

    def test_unet_infer_output_type(self):
        """Test that unet inference returns correct data type."""
        test_image = np.random.randint(0, 255, (32, 32), dtype=np.uint8)
        result = unet_infer(test_image)
        
        # Should return float32 numpy array
        assert isinstance(result, np.ndarray)
        assert result.dtype == np.float32


if __name__ == "__main__":
    pytest.main([__file__])
