import asyncio

from heliovision.camera.base import <PERSON>ame, FrameQueue
from loguru import logger

from src.analyzer import AnalyzerProtocol
from src.communication.botko_nodes import BotkoOPCUANode
from src.communication.opcua import OPCClientProtocol
from src.communication.profiler import ProfilerProtocol
from src.detections.free_space import EmptyRect<PERSON>rea
from src.gui.gui import GUIProtocol
from src.product.passport import Passport


async def monitor_faulted(opc_client: OPCClientProtocol, cancel_event: asyncio.Event) -> None:
    """Monitor BOTKO_FAULTED node and set the cancel event when it is True."""
    logger.debug('Starting to monitor BOTKO_FAULTED node...')
    while not cancel_event.is_set():
        try:
            faulted = await opc_client.receive(BotkoOPCUANode.BOTKO_FAULTED)
            if faulted:
                logger.warning('BOTKO_FAULTED is True, setting cancel event.')
                cancel_event.set()
                break
        except Exception as e:
            logger.error(f'Error while monitoring BOTKO_FAULTED: {e}')
            break
        await asyncio.sleep(0.5)  # Adjust the sleep time as needed


async def send_free_space(opc_client: OPCClientProtocol, free_space: EmptyRectArea) -> None:
    await opc_client.send(BotkoOPCUANode.HV_START_ROTATION_REQUEST, True)
    logger.debug('Sent rotation request')
    await asyncio.sleep(10)
    await opc_client.send(BotkoOPCUANode.HV_START_ROTATION_REQUEST, False)
    logger.debug('Rotation done')
    await opc_client.send(BotkoOPCUANode.HV_ENGRAVING_RADIUS, free_space.r)
    await opc_client.send(BotkoOPCUANode.HV_ENGRAVING_ANGLE, free_space.theta)
    await opc_client.send(BotkoOPCUANode.HV_ENGRAVING_HEIGHT, free_space.z)
    await opc_client.send(BotkoOPCUANode.HV_ENGRAVING_LOCATION_READY, True)


async def send_passport(opc_client: OPCClientProtocol, passport: Passport) -> None:
    await opc_client.send(BotkoOPCUANode.HV_PASSPORT_OWNER_NAME, passport.owner_name)
    await opc_client.send(BotkoOPCUANode.HV_PASSPORT_OWNER_CODE, passport.owner_code)
    await opc_client.send(BotkoOPCUANode.HV_PASSPORT_SERIAL_NUMBER, passport.serial_number)
    await opc_client.send(
        BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_NAME, passport.manufacturer_name
    )
    await opc_client.send(
        BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_CODE, passport.manufacturer_code
    )
    await opc_client.send(
        BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_SERIAL_NUMBER,
        passport.manufacturer_serial_number,
    )
    await opc_client.send(
        BotkoOPCUANode.HV_PASSPORT_DATE_MANUFACTURING, passport.manufacturing_date
    )
    await opc_client.send(BotkoOPCUANode.HV_PASSPORT_DATE_LAST_TEST, passport.last_test_date)
    await opc_client.send(BotkoOPCUANode.HV_PASSPORT_TEST_PRESSURE, passport.test_pressure)
    await opc_client.send(BotkoOPCUANode.HV_PASSPORT_CAPACITY, passport.capacity)
    await opc_client.send(
        BotkoOPCUANode.HV_PASSPORT_ORIGINAL_TARRA_WEIGHT, passport.original_tare_weight
    )
    await opc_client.send(BotkoOPCUANode.HV_PASSPORT_READY, True)


async def initialize(
    profiler: ProfilerProtocol, opc_client: OPCClientProtocol, gui: GUIProtocol
) -> FrameQueue:
    logger.info('Initializing the main flow...')
    try:
        frame_queue = await profiler.start_run()
        logger.info('Profiler started successfully.')
        await opc_client.initialize(
            BotkoOPCUANode.HV_LIVE_BIT,
            BotkoOPCUANode.BOTKO_LIVE_BIT,
            heartbeat_send_period=2.0,
            heartbeat_monitor_timeout=5.0,
        )
        logger.info('OPC client initialized successfully.')
        await gui.initialize()
        logger.info('GUI initialized successfully.')
        return frame_queue
    except Exception as e:
        logger.error(f'Failed to properly initialize: {e}')
        raise


async def flow_iteration(
    profiler: ProfilerProtocol,
    frame_queue: FrameQueue,
    opc_client: OPCClientProtocol,
    analyzer: AnalyzerProtocol,
    gui: GUIProtocol,
) -> None:
    logger.debug('Flow iteration started.')
    try:
        logger.debug('Resetting before start')
        await opc_client.send(BotkoOPCUANode.HV_SCAN_FINISHED, False)
        await opc_client.send(BotkoOPCUANode.HV_ENGRAVING_LOCATION_READY, False)
        await opc_client.send(BotkoOPCUANode.HV_PASSPORT_READY, False)
        logger.debug('Awaiting length and diameter and UT/HT')
        length = await opc_client.await_for_nonzero_value(BotkoOPCUANode.BOTKO_LENGTH)
        diameter = await opc_client.await_for_nonzero_value(BotkoOPCUANode.BOTKO_DIAMETER)
        ut_ht = await opc_client.receive(BotkoOPCUANode.BOTKO_UT_HT)
        await gui.clear()
        logger.debug(f'Got length {length}, diameter {diameter}, UT/HT: {ut_ht}')
        profiler.update_settings(length=length, diameter=diameter)
        logger.debug('Awaiting scan request')
        await opc_client.await_for_value(BotkoOPCUANode.BOTKO_SCAN_REQUEST, True)
        logger.debug('Got scan request')
        await opc_client.send(BotkoOPCUANode.HV_SCAN_ACKNOWLEDGE, True)
        logger.debug('Acknowledged scan')
        profiler.trigger()
        await opc_client.send(BotkoOPCUANode.HV_SCAN_BUSY, True)
        frame: Frame = await frame_queue.get()
        await opc_client.send(BotkoOPCUANode.HV_SCAN_FINISHED, True)
        await opc_client.send(BotkoOPCUANode.HV_SCAN_BUSY, False)
        await opc_client.send(BotkoOPCUANode.HV_SCAN_ACKNOWLEDGE, False)
        await opc_client.send(BotkoOPCUANode.BOTKO_DIAMETER, 0)
        await opc_client.send(BotkoOPCUANode.BOTKO_LENGTH, 0)
        logger.debug('Sending scan finished')
        free_spaces = analyzer.get_free_space(frame.image)
        if len(free_spaces) == 0:
            logger.warning('No free spaces detected in the frame.')
            await opc_client.send(BotkoOPCUANode.HV_FAULTED, True)
            return
        await send_free_space(opc_client, free_spaces[0])
        logger.debug(f'Sent free space {free_spaces[0]}')
        passport = analyzer.get_passport_from_ocr(frame.image)
        await send_passport(opc_client, passport)
        logger.debug(f'Sent passport: {passport}')
        await gui.new_measurement(passport, frame.image)
        await opc_client.await_for_value(BotkoOPCUANode.BOTKO_PASSPORT_ACKNOWLEDGE, True)

    except Exception as e:
        logger.error(f'Error during flow iteration: {e}')
        raise


async def main():
    import sys
    from pathlib import Path

    from heliovision.config import config

    from src.analyzer import MockAnalyzer
    from src.communication.opcua import OPCClient
    from src.communication.profiler import MockProfiler
    from src.gui.gui import Gui

    logger.remove()
    logger.add(sys.stderr, level='DEBUG')

    logger.info('Starting the main application...')
    profiler = MockProfiler(data_folder=Path('./data'), delay=5.0)
    opc_client = OPCClient(
        server_url=config.get_setting('opcua', 'server_url'),
        node_descriptions=BotkoOPCUANode,
    )
    gui = Gui()
    analyzer = MockAnalyzer()
    try:
        frame_queue = await initialize(profiler, opc_client, gui)
        logger.info('Initialization completed successfully.')
        while True:
            await opc_client.await_for_value(BotkoOPCUANode.BOTKO_FAULTED, False)
            cancel_event = asyncio.Event()
            flow_task = asyncio.create_task(
                flow_iteration(profiler, frame_queue, opc_client, analyzer, gui)
            )
            monitor_task = asyncio.create_task(monitor_faulted(opc_client, cancel_event))

            done, pending = await asyncio.wait(
                [flow_task, monitor_task], return_when=asyncio.FIRST_COMPLETED
            )
            if cancel_event.is_set():
                logger.warning('Cancel event was set, stopping the flow iteration.')
                try:
                    flow_task.cancel()
                except asyncio.CancelledError:
                    logger.debug('Flow task was cancelled successfully.')
                continue
            if flow_task in done and flow_task.exception():
                logger.error(f'Flow task raised an exception: {flow_task.exception()}')
                await opc_client.send(BotkoOPCUANode.HV_FAULTED, True)
    except Exception as e:
        logger.error(f'An error occurred in the main flow: {e}')
        await opc_client.send(BotkoOPCUANode.HV_FAULTED, True)
    finally:
        logger.info('Shutting down the application...')
        await gui.clear()
        logger.info('Application shutdown complete.')


if __name__ == '__main__':
    asyncio.run(main())
